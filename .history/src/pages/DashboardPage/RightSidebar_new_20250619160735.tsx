import React from 'react';
import type { DashboardData, HighRiskEvent } from '@/types/data';
import { useState, useEffect } from 'react';
import EmailNotification from '@/components/EmailNotification';

interface RightSidebarProps {
  dashboardData: DashboardData;
  highRiskEvents: HighRiskEvent[];
  width: number;
}

const RightSidebar: React.FC<RightSidebarProps> = ({
  dashboardData,
  highRiskEvents,
  width,
}) => {
  const [activeTab, setActiveTab] = useState<'attack' | 'host'>('attack');

  // 自动切换标签页
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTab(prev => prev === 'attack' ? 'host' : 'attack');
    }, 10000); // 每10秒切换一次

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="flex flex-col h-full gap-3 p-3" style={{ width: `${width}px` }}>
      {/* 邮件通知 */}
      <EmailNotification />
      
      {/* S6000 网省联动 - 左右滑动五个表格 */}
      <div
        className="bg-black p-3 rounded-lg shadow-glow-blue flex-grow min-h-0 border border-[#00d9ff]/20"
        style={{ minHeight: '50%' }}
      >
        <h2 className="text-sm font-medium relative flex items-center justify-between mb-3">
          <span className="text-[#00d9ff] uppercase tracking-widest">S6000 网省联动</span>
        </h2>

        <div className="h-full overflow-hidden pr-1">
          {/* 整体左右滑动容器 */}
          <div className="h-full overflow-hidden">
            <div
              className="flex h-full transition-transform duration-1000 ease-in-out"
              style={{
                width: '500%',
                animation: `s6000HorizontalScroll 30s infinite ease-in-out`
              }}
            >
              <style>{`
                @keyframes s6000HorizontalScroll {
                  0% { transform: translateX(0%); }
                  18% { transform: translateX(0%); }
                  20% { transform: translateX(-20%); }
                  38% { transform: translateX(-20%); }
                  40% { transform: translateX(-40%); }
                  58% { transform: translateX(-40%); }
                  60% { transform: translateX(-60%); }
                  78% { transform: translateX(-60%); }
                  80% { transform: translateX(-80%); }
                  98% { transform: translateX(-80%); }
                  100% { transform: translateX(0%); }
                }
              `}</style>

              {/* 1. 工作任务 - 三栏布局 */}
              <div className="w-full h-full flex-shrink-0 px-2">
                <div className="bg-slate-900/40 rounded-lg border border-slate-700/30 h-full">
                  <h3 className="text-xs font-medium text-[#ffb74d] px-3 py-2 border-b border-slate-700/30">工作任务</h3>
                  {dashboardData.workTasks && dashboardData.workTasks.length > 0 ? (
                    <div className="flex flex-col h-[calc(100%-40px)]">
                      {/* 上栏：状态可视化 */}
                      <div className="h-8 px-3 py-1 bg-slate-800/50 border-b border-slate-700/30 flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                            <span className="text-xxs text-orange-300">未反馈: {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="text-xxs text-green-300">已反馈: {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').length}</span>
                          </div>
                        </div>
                        {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length === 0 && (
                          <div className="flex items-center gap-1">
                            <div className="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xxs">✓</span>
                            </div>
                            <span className="text-xxs text-green-300 font-medium">全部已反馈</span>
                          </div>
                        )}
                      </div>
                      
                      {/* 中栏：未反馈滑动 */}
                      {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length > 0 && (
                        <div className="flex-1 overflow-hidden border-b border-slate-700/30" style={{ minHeight: '250px' }}>
                          <div className="h-full overflow-hidden px-1 pt-1">
                            <div
                              className="infinite-scroll-list"
                              style={{
                                animation: `workTasksUnfinishedScroll ${dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length * 4}s infinite ease-in-out`
                              }}
                            >
                              <style>{`
                                @keyframes workTasksUnfinishedScroll {
                                  ${dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((_, index) => {
                                    const totalItems = dashboardData.workTasks?.filter(t => t.feedback_status === '未反馈').length || 0;
                                    const itemDuration = 100 / totalItems;
                                    const stayPercent = itemDuration * 0.75;
                                    const startPercent = index * itemDuration;
                                    const stayEndPercent = startPercent + stayPercent;
                                    const endPercent = (index + 1) * itemDuration;
                                    const currentPosition = -index * 120;
                                    const nextPosition = -(index + 1) * 120;
                                    return `
                                      ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                      ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                      ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                                    `;
                                  }).join('')}
                                  100% { transform: translateY(-${(dashboardData.workTasks?.filter(t => t.feedback_status === '未反馈').length || 0) * 120}px); }
                                }
                              `}</style>
                              {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((task) => (
                                <div key={`task-unfinished-${task.id}`} className="flex flex-col py-3 px-4 bg-orange-900/30 rounded-lg mb-2 border border-orange-500/30" style={{height: '110px', minHeight: '110px'}}>
                                  <div className="flex items-start justify-between mb-1">
                                    <div className="flex items-center gap-2">
                                      <span className="text-xxs font-medium px-2 py-0.5 rounded bg-orange-500/25 text-orange-300">{task.type}</span>
                                      <h3 className="font-semibold text-[#ffb74d] text-sm leading-tight">{task.title}</h3>
                                    </div>
                                    <div className="flex flex-col items-center">
                                      <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse mb-1"></div>
                                      <span className="text-xxs text-orange-400 font-medium">未反馈</span>
                                    </div>
                                  </div>
                                  <p className="text-orange-200 text-xxs mb-2 leading-relaxed truncate">{task.description}</p>
                                  <div className="flex items-center justify-between text-xxs text-slate-400">
                                    <span>编号: {task.task_id}</span>
                                    <div className="flex items-center gap-2">
                                      <span>发布: {new Date(task.publish_time).toLocaleDateString()}</span>
                                      <span className="text-orange-300">截止: {new Date(task.deadline_time).toLocaleDateString()}</span>
                                    </div>
                                  </div>
                                </div>
                              ))}
                              {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((task) => (
                                <div key={`task-unfinished-repeat-${task.id}`} className="flex flex-col py-3 px-4 bg-orange-900/30 rounded-lg mb-2 border border-orange-500/30" style={{height: '110px', minHeight: '110px'}}>
                                  <div className="flex items-start justify-between mb-1">
                                    <div className="flex items-center gap-2">
                                      <span className="text-xxs font-medium px-2 py-0.5 rounded bg-orange-500/25 text-orange-300">{task.type}</span>
                                      <h3 className="font-semibold text-[#ffb74d] text-sm leading-tight">{task.title}</h3>
                                    </div>
                                    <div className="flex flex-col items-center">
                                      <div className="w-3 h-3 bg-orange-500 rounded-full animate-pulse mb-1"></div>
                                      <span className="text-xxs text-orange-400 font-medium">未反馈</span>
                                    </div>
                                  </div>
                                  <p className="text-orange-200 text-xxs mb-2 leading-relaxed truncate">{task.description}</p>
                                  <div className="flex items-center justify-between text-xxs text-slate-400">
                                    <span>编号: {task.task_id}</span>
                                    <div className="flex items-center gap-2">
                                      <span>发布: {new Date(task.publish_time).toLocaleDateString()}</span>
                                      <span className="text-orange-300">截止: {new Date(task.deadline_time).toLocaleDateString()}</span>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {/* 警戒线分隔 */}
                      <div className="relative py-2">
                        <div className="absolute inset-0 flex items-center">
                          <div className="w-full border-t border-dashed border-yellow-500/50"></div>
                        </div>
                        <div className="relative flex justify-center text-xxs">
                          <span className="bg-black px-2 text-yellow-400 font-medium">已反馈任务</span>
                        </div>
                      </div>

                      {/* 下栏：已反馈滑动 */}
                      <div className="flex-1 overflow-hidden" style={{ minHeight: '250px' }}>
                        <div className="h-full overflow-hidden px-1 pt-1">
                          <div
                            className="infinite-scroll-list"
                            style={{
                              animation: `workTasksFinishedScroll ${dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').length * 4}s infinite ease-in-out`
                            }}
                          >
                            <style>{`
                              @keyframes workTasksFinishedScroll {
                                ${dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').map((_, index) => {
                                  const totalItems = dashboardData.workTasks?.filter(t => t.feedback_status === '已反馈').length || 0;
                                  const itemDuration = 100 / totalItems;
                                  const stayPercent = itemDuration * 0.75;
                                  const startPercent = index * itemDuration;
                                  const stayEndPercent = startPercent + stayPercent;
                                  const endPercent = (index + 1) * itemDuration;
                                  const currentPosition = -index * 120;
                                  const nextPosition = -(index + 1) * 120;
                                  return `
                                    ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                    ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                    ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                                  `;
                                }).join('')}
                                100% { transform: translateY(-${(dashboardData.workTasks?.filter(t => t.feedback_status === '已反馈').length || 0) * 120}px); }
                              }
                            `}</style>
                            {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').map((task) => (
                              <div key={`task-finished-${task.id}`} className="flex flex-col py-3 px-4 bg-green-900/30 rounded-lg mb-2 border border-green-500/30" style={{height: '110px', minHeight: '110px'}}>
                                <div className="flex items-start justify-between mb-1">
                                  <div className="flex items-center gap-2">
                                    <span className="text-xxs font-medium px-2 py-0.5 rounded bg-green-500/25 text-green-300">{task.type}</span>
                                    <h3 className="font-semibold text-green-200 text-sm leading-tight">{task.title}</h3>
                                  </div>
                                  <div className="flex flex-col items-center">
                                    <div className="w-3 h-3 bg-green-500 rounded-full mb-1"></div>
                                    <span className="text-xxs text-green-400 font-medium">已反馈</span>
                                  </div>
                                </div>
                                <p className="text-green-200 text-xxs mb-2 leading-relaxed truncate">{task.description}</p>
                                <div className="flex items-center justify-between text-xxs text-slate-500">
                                  <span>编号: {task.task_id}</span>
                                  <div className="flex items-center gap-2">
                                    <span>反馈: {task.feedback_person}</span>
                                    <span>发布: {new Date(task.publish_time).toLocaleDateString()}</span>
                                  </div>
                                </div>
                              </div>
                            ))}
                            {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').map((task) => (
                              <div key={`task-finished-repeat-${task.id}`} className="flex flex-col py-3 px-4 bg-green-900/30 rounded-lg mb-2 border border-green-500/30" style={{height: '110px', minHeight: '110px'}}>
                                <div className="flex items-start justify-between mb-1">
                                  <div className="flex items-center gap-2">
                                    <span className="text-xxs font-medium px-2 py-0.5 rounded bg-green-500/25 text-green-300">{task.type}</span>
                                    <h3 className="font-semibold text-green-200 text-sm leading-tight">{task.title}</h3>
                                  </div>
                                  <div className="flex flex-col items-center">
                                    <div className="w-3 h-3 bg-green-500 rounded-full mb-1"></div>
                                    <span className="text-xxs text-green-400 font-medium">已反馈</span>
                                  </div>
                                </div>
                                <p className="text-green-200 text-xxs mb-2 leading-relaxed truncate">{task.description}</p>
                                <div className="flex items-center justify-between text-xxs text-slate-500">
                                  <span>编号: {task.task_id}</span>
                                  <div className="flex items-center gap-2">
                                    <span>反馈: {task.feedback_person}</span>
                                    <span>发布: {new Date(task.publish_time).toLocaleDateString()}</span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <p className="text-slate-500 text-xs p-3">无工作任务</p>
                  )}
                </div>
              </div>

              {/* 2. 工作通知 - 单栏滑动 */}
              <div className="w-full h-full flex-shrink-0 px-2">
                <div className="bg-slate-900/40 rounded-lg border border-slate-700/30 h-full">
                  <h3 className="text-xs font-medium text-blue-300 px-3 py-2 border-b border-slate-700/30">工作通知</h3>
                  {dashboardData.workNotifications && dashboardData.workNotifications.length > 0 ? (
                    <div className="h-[calc(100%-40px)] overflow-hidden px-1 pt-1">
                      <div
                        className="infinite-scroll-list"
                        style={{
                          animation: `workNotificationsScroll ${dashboardData.workNotifications.length * 4}s infinite ease-in-out`
                        }}
                      >
                        <style>{`
                          @keyframes workNotificationsScroll {
                            ${dashboardData.workNotifications.map((_, index) => {
                              const totalItems = dashboardData.workNotifications?.length || 0;
                              const itemDuration = 100 / totalItems;
                              const stayPercent = itemDuration * 0.75;
                              const startPercent = index * itemDuration;
                              const stayEndPercent = startPercent + stayPercent;
                              const endPercent = (index + 1) * itemDuration;
                              const currentPosition = -index * 105;
                              const nextPosition = -(index + 1) * 105;
                              return `
                                ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                              `;
                            }).join('')}
                            100% { transform: translateY(-${(dashboardData.workNotifications?.length || 0) * 105}px); }
                          }
                        `}</style>
                        {dashboardData.workNotifications.map((notification) => (
                          <div key={`notification-${notification.id}`} className="flex flex-col py-3 px-4 bg-blue-900/30 rounded-lg mb-2 border border-blue-500/30" style={{height: '95px', minHeight: '95px'}}>
                            <div className="flex items-start justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xxs font-medium px-2 py-0.5 rounded bg-blue-500/25 text-blue-300">{notification.type}</span>
                                <h3 className="font-semibold text-blue-200 text-sm leading-tight">{notification.title}</h3>
                              </div>
                            </div>
                            <p className="text-blue-200 text-xxs mb-2 leading-relaxed truncate">{notification.description}</p>
                            <div className="flex items-center justify-between text-xxs text-slate-400">
                              <span>编号: {notification.notification_id}</span>
                              <span>发布: {new Date(notification.publish_time).toLocaleDateString()}</span>
                            </div>
                          </div>
                        ))}
                        {dashboardData.workNotifications.map((notification) => (
                          <div key={`notification-repeat-${notification.id}`} className="flex flex-col py-3 px-4 bg-blue-900/30 rounded-lg mb-2 border border-blue-500/30" style={{height: '95px', minHeight: '95px'}}>
                            <div className="flex items-start justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xxs font-medium px-2 py-0.5 rounded bg-blue-500/25 text-blue-300">{notification.type}</span>
                                <h3 className="font-semibold text-blue-200 text-sm leading-tight">{notification.title}</h3>
                              </div>
                            </div>
                            <p className="text-blue-200 text-xxs mb-2 leading-relaxed truncate">{notification.description}</p>
                            <div className="flex items-center justify-between text-xxs text-slate-400">
                              <span>编号: {notification.notification_id}</span>
                              <span>发布: {new Date(notification.publish_time).toLocaleDateString()}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <p className="text-slate-500 text-xs p-3">无工作通知</p>
                  )}
                </div>
              </div>

              {/* 3. 攻击源预警 - 单栏滑动 */}
              <div className="w-full h-full flex-shrink-0 px-2">
                <div className="bg-slate-900/40 rounded-lg border border-slate-700/30 h-full">
                  <h3 className="text-xs font-medium text-red-300 px-3 py-2 border-b border-slate-700/30">攻击源预警</h3>
                  {dashboardData.attackSourceWarnings && dashboardData.attackSourceWarnings.length > 0 ? (
                    <div className="h-[calc(100%-40px)] overflow-hidden px-1 pt-1">
                      <div
                        className="infinite-scroll-list"
                        style={{
                          animation: `attackSourceScroll ${dashboardData.attackSourceWarnings.length * 4}s infinite ease-in-out`
                        }}
                      >
                        <style>{`
                          @keyframes attackSourceScroll {
                            ${dashboardData.attackSourceWarnings.map((_, index) => {
                              const totalItems = dashboardData.attackSourceWarnings?.length || 0;
                              const itemDuration = 100 / totalItems;
                              const stayPercent = itemDuration * 0.75;
                              const startPercent = index * itemDuration;
                              const stayEndPercent = startPercent + stayPercent;
                              const endPercent = (index + 1) * itemDuration;
                              const currentPosition = -index * 105;
                              const nextPosition = -(index + 1) * 105;
                              return `
                                ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                              `;
                            }).join('')}
                            100% { transform: translateY(-${(dashboardData.attackSourceWarnings?.length || 0) * 105}px); }
                          }
                        `}</style>
                        {dashboardData.attackSourceWarnings.map((warning) => (
                          <div key={`attack-warning-${warning.id}`} className="flex flex-col py-3 px-4 bg-red-900/30 rounded-lg mb-2 border border-red-500/30" style={{height: '95px', minHeight: '95px'}}>
                            <div className="flex items-start justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xxs font-medium px-2 py-0.5 rounded bg-red-500/25 text-red-300">攻击源预警</span>
                                <h3 className="font-semibold text-red-200 text-sm leading-tight">{warning.title}</h3>
                              </div>
                            </div>
                            <p className="text-red-200 text-xxs mb-2 leading-relaxed truncate">{warning.description}</p>
                            <div className="flex items-center justify-between text-xxs text-slate-400">
                              <span>编号: {warning.warning_id}</span>
                              <div className="flex items-center gap-2">
                                <span>点击: {warning.hits}</span>
                                <span>发布: {new Date(warning.publish_time).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                        {dashboardData.attackSourceWarnings.map((warning) => (
                          <div key={`attack-warning-repeat-${warning.id}`} className="flex flex-col py-3 px-4 bg-red-900/30 rounded-lg mb-2 border border-red-500/30" style={{height: '95px', minHeight: '95px'}}>
                            <div className="flex items-start justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className="text-xxs font-medium px-2 py-0.5 rounded bg-red-500/25 text-red-300">攻击源预警</span>
                                <h3 className="font-semibold text-red-200 text-sm leading-tight">{warning.title}</h3>
                              </div>
                            </div>
                            <p className="text-red-200 text-xxs mb-2 leading-relaxed truncate">{warning.description}</p>
                            <div className="flex items-center justify-between text-xxs text-slate-400">
                              <span>编号: {warning.warning_id}</span>
                              <div className="flex items-center gap-2">
                                <span>点击: {warning.hits}</span>
                                <span>发布: {new Date(warning.publish_time).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <p className="text-slate-500 text-xs p-3">无攻击源预警</p>
                  )}
                </div>
              </div>
