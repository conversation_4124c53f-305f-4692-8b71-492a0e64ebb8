import React from 'react';
import type { DashboardData, HighRiskEvent } from '@/types/data';
import { useState, useEffect } from 'react';
import EmailNotification from '@/components/EmailNotification';

interface RightSidebarProps {
  dashboardData: DashboardData;
  highRiskEvents: HighRiskEvent[];
  width: number;
}

const RightSidebar: React.FC<RightSidebarProps> = ({
  dashboardData,
  highRiskEvents,
  width,
}) => {
  const [activeTab, setActiveTab] = useState<'attack' | 'host'>('attack');

  // 自动切换标签页
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTab(prev => prev === 'attack' ? 'host' : 'attack');
    }, 10000); // 每10秒切换一次

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="flex flex-col h-full gap-3 p-3" style={{ width: `${width}px` }}>
      {/* 邮件通知 */}
      <EmailNotification />
      
      {/* S6000 网省联动 - 瀑布流五个表格 */}
      <div
        className="bg-black p-3 rounded-lg shadow-glow-blue flex-grow min-h-0 border border-[#00d9ff]/20"
        style={{ minHeight: '35%' }}
      >
        <h2 className="text-sm font-medium relative flex items-center justify-between mb-3">
          <span className="text-[#00d9ff] uppercase tracking-widest">S6000 网省联动</span>
        </h2>
        
        <div className="h-full overflow-y-auto pr-1 space-y-3">
          {/* 1. 工作任务 - 三栏布局 */}
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30">
            <h3 className="text-xs font-medium text-[#ffb74d] px-3 py-2 border-b border-slate-700/30">工作任务</h3>
            {dashboardData.workTasks && dashboardData.workTasks.length > 0 ? (
              <div className="flex flex-col h-48">
                {/* 上栏：状态可视化 */}
                <div className="h-8 px-3 py-1 bg-slate-800/50 border-b border-slate-700/30 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-xxs text-orange-300">未反馈: {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-xxs text-green-300">已反馈: {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').length}</span>
                    </div>
                  </div>
                  {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length === 0 && (
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xxs">✓</span>
                      </div>
                      <span className="text-xxs text-green-300 font-medium">全部已反馈</span>
                    </div>
                  )}
                </div>
                
                {/* 中栏：未反馈滑动 */}
                {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length > 0 && (
                  <div className="flex-1 overflow-hidden border-b border-slate-700/30">
                    <div className="h-full overflow-hidden px-1 pt-1">
                      <div
                        className="infinite-scroll-list"
                        style={{
                          animation: `workTasksUnfinishedScroll ${dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length * 4}s infinite ease-in-out`
                        }}
                      >
                        <style>{`
                          @keyframes workTasksUnfinishedScroll {
                            ${dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((_, index) => {
                              const totalItems = dashboardData.workTasks?.filter(t => t.feedback_status === '未反馈').length || 0;
                              const itemDuration = 100 / totalItems;
                              const stayPercent = itemDuration * 0.75;
                              const startPercent = index * itemDuration;
                              const stayEndPercent = startPercent + stayPercent;
                              const endPercent = (index + 1) * itemDuration;
                              const currentPosition = -index * 50;
                              const nextPosition = -(index + 1) * 50;
                              return `
                                ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                              `;
                            }).join('')}
                            100% { transform: translateY(-${(dashboardData.workTasks?.filter(t => t.feedback_status === '未反馈').length || 0) * 50}px); }
                          }
                        `}</style>
                        {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((task) => (
                          <div key={`task-unfinished-${task.id}`} className="flex items-center justify-between py-1 px-2 bg-orange-900/40 rounded mb-1 border border-orange-500/40" style={{height: '46px', minHeight: '46px'}}>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 mb-0.5">
                                <span className="text-xxs font-bold px-1 py-0.5 rounded bg-orange-500/30 text-orange-200">{task.task_id}</span>
                                <span className="text-xxs text-slate-500">{task.type}</span>
                              </div>
                              <h4 className="font-medium text-orange-200 text-xxs leading-tight truncate">{task.title}</h4>
                            </div>
                            <div className="flex flex-col items-center ml-2">
                              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse mb-0.5"></div>
                              <span className="text-xxs text-orange-400 font-bold">未反馈</span>
                            </div>
                          </div>
                        ))}
                        {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((task) => (
                          <div key={`task-unfinished-repeat-${task.id}`} className="flex items-center justify-between py-1 px-2 bg-orange-900/40 rounded mb-1 border border-orange-500/40" style={{height: '46px', minHeight: '46px'}}>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 mb-0.5">
                                <span className="text-xxs font-bold px-1 py-0.5 rounded bg-orange-500/30 text-orange-200">{task.task_id}</span>
                                <span className="text-xxs text-slate-500">{task.type}</span>
                              </div>
                              <h4 className="font-medium text-orange-200 text-xxs leading-tight truncate">{task.title}</h4>
                            </div>
                            <div className="flex flex-col items-center ml-2">
                              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse mb-0.5"></div>
                              <span className="text-xxs text-orange-400 font-bold">未反馈</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
                
                {/* 下栏：已反馈滑动 */}
                <div className="flex-1 overflow-hidden">
                  <div className="h-full overflow-y-auto px-1 pt-1">
                    {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').map((task) => (
                      <div key={`task-finished-${task.id}`} className="flex items-center justify-between py-1 px-2 bg-green-900/30 rounded mb-1 border border-green-500/30" style={{height: '46px', minHeight: '46px'}}>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-1 mb-0.5">
                            <span className="text-xxs font-bold px-1 py-0.5 rounded bg-green-500/30 text-green-200">{task.task_id}</span>
                            <span className="text-xxs text-slate-600">{task.type}</span>
                          </div>
                          <h4 className="font-medium text-green-200 text-xxs leading-tight truncate">{task.title}</h4>
                        </div>
                        <div className="flex flex-col items-center ml-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full mb-0.5"></div>
                          <span className="text-xxs text-green-400 font-bold">已反馈</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无工作任务</p>
            )}
          </div>

          {/* 2. 工作通知 - 单栏滑动 */}
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30">
            <h3 className="text-xs font-medium text-blue-300 px-3 py-2 border-b border-slate-700/30">工作通知</h3>
            {dashboardData.workNotifications && dashboardData.workNotifications.length > 0 ? (
              <div className="h-32 overflow-hidden px-1 pt-1">
                <div
                  className="infinite-scroll-list"
                  style={{
                    animation: `workNotificationsScroll ${dashboardData.workNotifications.length * 4}s infinite ease-in-out`
                  }}
                >
                  <style>{`
                    @keyframes workNotificationsScroll {
                      ${dashboardData.workNotifications.map((_, index) => {
                        const totalItems = dashboardData.workNotifications?.length || 0;
                        const itemDuration = 100 / totalItems;
                        const stayPercent = itemDuration * 0.75;
                        const startPercent = index * itemDuration;
                        const stayEndPercent = startPercent + stayPercent;
                        const endPercent = (index + 1) * itemDuration;
                        const currentPosition = -index * 50;
                        const nextPosition = -(index + 1) * 50;
                        return `
                          ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                        `;
                      }).join('')}
                      100% { transform: translateY(-${(dashboardData.workNotifications?.length || 0) * 50}px); }
                    }
                  `}</style>
                  {dashboardData.workNotifications.map((notification) => (
                    <div key={`notification-${notification.id}`} className="flex items-center justify-between py-1 px-2 bg-blue-900/30 rounded mb-1 border border-blue-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-blue-500/30 text-blue-200">{notification.notification_id}</span>
                          <span className="text-xxs text-blue-400">{notification.type}</span>
                        </div>
                        <h4 className="font-medium text-blue-200 text-xxs leading-tight truncate">{notification.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(notification.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                  {dashboardData.workNotifications.map((notification) => (
                    <div key={`notification-repeat-${notification.id}`} className="flex items-center justify-between py-1 px-2 bg-blue-900/30 rounded mb-1 border border-blue-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-blue-500/30 text-blue-200">{notification.notification_id}</span>
                          <span className="text-xxs text-blue-400">{notification.type}</span>
                        </div>
                        <h4 className="font-medium text-blue-200 text-xxs leading-tight truncate">{notification.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(notification.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无工作通知</p>
            )}
          </div>

          {/* 3. 攻击源预警 - 单栏滑动 */}
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30">
            <h3 className="text-xs font-medium text-red-300 px-3 py-2 border-b border-slate-700/30">攻击源预警</h3>
            {dashboardData.attackSourceWarnings && dashboardData.attackSourceWarnings.length > 0 ? (
              <div className="h-32 overflow-hidden px-1 pt-1">
                <div
                  className="infinite-scroll-list"
                  style={{
                    animation: `attackSourceScroll ${dashboardData.attackSourceWarnings.length * 4}s infinite ease-in-out`
                  }}
                >
                  <style>{`
                    @keyframes attackSourceScroll {
                      ${dashboardData.attackSourceWarnings.map((_, index) => {
                        const totalItems = dashboardData.attackSourceWarnings?.length || 0;
                        const itemDuration = 100 / totalItems;
                        const stayPercent = itemDuration * 0.75;
                        const startPercent = index * itemDuration;
                        const stayEndPercent = startPercent + stayPercent;
                        const endPercent = (index + 1) * itemDuration;
                        const currentPosition = -index * 50;
                        const nextPosition = -(index + 1) * 50;
                        return `
                          ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                        `;
                      }).join('')}
                      100% { transform: translateY(-${(dashboardData.attackSourceWarnings?.length || 0) * 50}px); }
                    }
                  `}</style>
                  {dashboardData.attackSourceWarnings.map((warning) => (
                    <div key={`attack-warning-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-red-900/30 rounded mb-1 border border-red-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-red-500/30 text-red-200">{warning.warning_id}</span>
                          <span className="text-xxs text-red-400">点击: {warning.hits}</span>
                        </div>
                        <h4 className="font-medium text-red-200 text-xxs leading-tight truncate">{warning.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(warning.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                  {dashboardData.attackSourceWarnings.map((warning) => (
                    <div key={`attack-warning-repeat-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-red-900/30 rounded mb-1 border border-red-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-red-500/30 text-red-200">{warning.warning_id}</span>
                          <span className="text-xxs text-red-400">点击: {warning.hits}</span>
                        </div>
                        <h4 className="font-medium text-red-200 text-xxs leading-tight truncate">{warning.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(warning.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无攻击源预警</p>
            )}
          </div>

          {/* 4. 漏洞预警 - 三栏布局 */}
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30">
            <h3 className="text-xs font-medium text-orange-300 px-3 py-2 border-b border-slate-700/30">漏洞预警</h3>
            {dashboardData.vulnerabilityWarnings && dashboardData.vulnerabilityWarnings.length > 0 ? (
              <div className="flex flex-col h-48">
                {/* 上栏：状态可视化 */}
                <div className="h-8 px-3 py-1 bg-slate-800/50 border-b border-slate-700/30 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-xxs text-orange-300">进行中: {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-xxs text-green-300">已完成: {dashboardData.vulnerabilityWarnings.filter(v => v.status === '已完成').length}</span>
                    </div>
                  </div>
                  {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length === 0 && (
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xxs">✓</span>
                      </div>
                      <span className="text-xxs text-green-300 font-medium">全部已完成</span>
                    </div>
                  )}
                </div>

                {/* 中栏：进行中滑动 */}
                {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length > 0 && (
                  <div className="flex-1 overflow-hidden border-b border-slate-700/30">
                    <div className="h-full overflow-hidden px-1 pt-1">
                      <div
                        className="infinite-scroll-list"
                        style={{
                          animation: `vulnWarningsOngoingScroll ${dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length * 4}s infinite ease-in-out`
                        }}
                      >
                        <style>{`
                          @keyframes vulnWarningsOngoingScroll {
                            ${dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').map((_, index) => {
                              const totalItems = dashboardData.vulnerabilityWarnings?.filter(v => v.status === '进行中').length || 0;
                              const itemDuration = 100 / totalItems;
                              const stayPercent = itemDuration * 0.75;
                              const startPercent = index * itemDuration;
                              const stayEndPercent = startPercent + stayPercent;
                              const endPercent = (index + 1) * itemDuration;
                              const currentPosition = -index * 55;
                              const nextPosition = -(index + 1) * 55;
                              return `
                                ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                              `;
                            }).join('')}
                            100% { transform: translateY(-${(dashboardData.vulnerabilityWarnings?.filter(v => v.status === '进行中').length || 0) * 55}px); }
                          }
                        `}</style>
                        {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').map((warning) => (
                          <div key={`vuln-ongoing-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-orange-900/40 rounded mb-1 border border-orange-500/40" style={{height: '51px', minHeight: '51px'}}>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 mb-0.5">
                                <span className="text-xxs font-bold px-1 py-0.5 rounded bg-orange-500/30 text-orange-200">{warning.warning_id}</span>
                                <span className="text-xxs text-orange-400">{warning.risk_level}</span>
                              </div>
                              <h4 className="font-medium text-orange-200 text-xxs leading-tight truncate mb-0.5">{warning.title.split('\n')[0]}</h4>
                              <span className="text-xxs text-slate-400">反馈: {warning.feedback_deadline.split(' ')[0]}</span>
                            </div>
                            <div className="flex flex-col items-center ml-2">
                              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse mb-0.5"></div>
                              <span className="text-xxs text-orange-400 font-bold">进行中</span>
                            </div>
                          </div>
                        ))}
                        {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').map((warning) => (
                          <div key={`vuln-ongoing-repeat-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-orange-900/40 rounded mb-1 border border-orange-500/40" style={{height: '51px', minHeight: '51px'}}>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 mb-0.5">
                                <span className="text-xxs font-bold px-1 py-0.5 rounded bg-orange-500/30 text-orange-200">{warning.warning_id}</span>
                                <span className="text-xxs text-orange-400">{warning.risk_level}</span>
                              </div>
                              <h4 className="font-medium text-orange-200 text-xxs leading-tight truncate mb-0.5">{warning.title.split('\n')[0]}</h4>
                              <span className="text-xxs text-slate-400">反馈: {warning.feedback_deadline.split(' ')[0]}</span>
                            </div>
                            <div className="flex flex-col items-center ml-2">
                              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse mb-0.5"></div>
                              <span className="text-xxs text-orange-400 font-bold">进行中</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* 下栏：已完成滑动 */}
                <div className="flex-1 overflow-hidden">
                  <div className="h-full overflow-y-auto px-1 pt-1">
                    {dashboardData.vulnerabilityWarnings.filter(v => v.status === '已完成').map((warning) => (
                      <div key={`vuln-finished-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-green-900/30 rounded mb-1 border border-green-500/30" style={{height: '51px', minHeight: '51px'}}>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-1 mb-0.5">
                            <span className="text-xxs font-bold px-1 py-0.5 rounded bg-green-500/30 text-green-200">{warning.warning_id}</span>
                            <span className="text-xxs text-slate-600">{warning.risk_level}</span>
                          </div>
                          <h4 className="font-medium text-green-200 text-xxs leading-tight truncate mb-0.5">{warning.title.split('\n')[0]}</h4>
                          <span className="text-xxs text-slate-500">处理: {warning.feedback_person}</span>
                        </div>
                        <div className="flex flex-col items-center ml-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full mb-0.5"></div>
                          <span className="text-xxs text-green-400 font-bold">已完成</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无漏洞预警</p>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default RightSidebar;
