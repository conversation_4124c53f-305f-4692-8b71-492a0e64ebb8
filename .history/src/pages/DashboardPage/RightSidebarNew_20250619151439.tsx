import React from 'react';
import type { DashboardData, HighRiskEvent } from '@/types/data';
import { useState, useEffect } from 'react';
import EmailNotification from '@/components/EmailNotification';

interface RightSidebarProps {
  dashboardData: DashboardData;
  highRiskEvents: HighRiskEvent[];
  width: number;
}

const RightSidebar: React.FC<RightSidebarProps> = ({
  dashboardData,
  highRiskEvents,
  width,
}) => {
  const [activeTab, setActiveTab] = useState<'attack' | 'host'>('attack');

  // 自动切换标签页
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveTab(prev => prev === 'attack' ? 'host' : 'attack');
    }, 10000); // 每10秒切换一次

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="flex flex-col h-full gap-3 p-3" style={{ width: `${width}px` }}>
      {/* 邮件通知 */}
      <EmailNotification />
      
      {/* S6000 网省联动 - 瀑布流五个表格 */}
      <div
        className="bg-black p-3 rounded-lg shadow-glow-blue flex-grow min-h-0 border border-[#00d9ff]/20"
        style={{ minHeight: '35%' }}
      >
        <h2 className="text-sm font-medium relative flex items-center justify-between mb-3">
          <span className="text-[#00d9ff] uppercase tracking-widest">S6000 网省联动</span>
        </h2>
        
        <div className="h-full overflow-y-auto pr-1 space-y-3">
          {/* 1. 工作任务 - 三栏布局 */}
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30">
            <h3 className="text-xs font-medium text-[#ffb74d] px-3 py-2 border-b border-slate-700/30">工作任务</h3>
            {dashboardData.workTasks && dashboardData.workTasks.length > 0 ? (
              <div className="flex flex-col h-48">
                {/* 上栏：状态可视化 */}
                <div className="h-8 px-3 py-1 bg-slate-800/50 border-b border-slate-700/30 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-xxs text-orange-300">未反馈: {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-xxs text-green-300">已反馈: {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').length}</span>
                    </div>
                  </div>
                  {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length === 0 && (
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xxs">✓</span>
                      </div>
                      <span className="text-xxs text-green-300 font-medium">全部已反馈</span>
                    </div>
                  )}
                </div>
                
                {/* 中栏：未反馈滑动 */}
                {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length > 0 && (
                  <div className="flex-1 overflow-hidden border-b border-slate-700/30">
                    <div className="h-full overflow-hidden px-1 pt-1">
                      <div
                        className="infinite-scroll-list"
                        style={{
                          animation: `workTasksUnfinishedScroll ${dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').length * 4}s infinite ease-in-out`
                        }}
                      >
                        <style>{`
                          @keyframes workTasksUnfinishedScroll {
                            ${dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((_, index) => {
                              const totalItems = dashboardData.workTasks?.filter(t => t.feedback_status === '未反馈').length || 0;
                              const itemDuration = 100 / totalItems;
                              const stayPercent = itemDuration * 0.75;
                              const startPercent = index * itemDuration;
                              const stayEndPercent = startPercent + stayPercent;
                              const endPercent = (index + 1) * itemDuration;
                              const currentPosition = -index * 50;
                              const nextPosition = -(index + 1) * 50;
                              return `
                                ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                              `;
                            }).join('')}
                            100% { transform: translateY(-${(dashboardData.workTasks?.filter(t => t.feedback_status === '未反馈').length || 0) * 50}px); }
                          }
                        `}</style>
                        {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((task) => (
                          <div key={`task-unfinished-${task.id}`} className="flex items-center justify-between py-1 px-2 bg-orange-900/40 rounded mb-1 border border-orange-500/40" style={{height: '46px', minHeight: '46px'}}>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 mb-0.5">
                                <span className="text-xxs font-bold px-1 py-0.5 rounded bg-orange-500/30 text-orange-200">{task.task_id}</span>
                                <span className="text-xxs text-slate-500">{task.type}</span>
                              </div>
                              <h4 className="font-medium text-orange-200 text-xxs leading-tight truncate">{task.title}</h4>
                            </div>
                            <div className="flex flex-col items-center ml-2">
                              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse mb-0.5"></div>
                              <span className="text-xxs text-orange-400 font-bold">未反馈</span>
                            </div>
                          </div>
                        ))}
                        {dashboardData.workTasks.filter(t => t.feedback_status === '未反馈').map((task) => (
                          <div key={`task-unfinished-repeat-${task.id}`} className="flex items-center justify-between py-1 px-2 bg-orange-900/40 rounded mb-1 border border-orange-500/40" style={{height: '46px', minHeight: '46px'}}>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 mb-0.5">
                                <span className="text-xxs font-bold px-1 py-0.5 rounded bg-orange-500/30 text-orange-200">{task.task_id}</span>
                                <span className="text-xxs text-slate-500">{task.type}</span>
                              </div>
                              <h4 className="font-medium text-orange-200 text-xxs leading-tight truncate">{task.title}</h4>
                            </div>
                            <div className="flex flex-col items-center ml-2">
                              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse mb-0.5"></div>
                              <span className="text-xxs text-orange-400 font-bold">未反馈</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
                
                {/* 下栏：已反馈滑动 */}
                <div className="flex-1 overflow-hidden">
                  <div className="h-full overflow-y-auto px-1 pt-1">
                    {dashboardData.workTasks.filter(t => t.feedback_status === '已反馈').map((task) => (
                      <div key={`task-finished-${task.id}`} className="flex items-center justify-between py-1 px-2 bg-green-900/30 rounded mb-1 border border-green-500/30" style={{height: '46px', minHeight: '46px'}}>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-1 mb-0.5">
                            <span className="text-xxs font-bold px-1 py-0.5 rounded bg-green-500/30 text-green-200">{task.task_id}</span>
                            <span className="text-xxs text-slate-600">{task.type}</span>
                          </div>
                          <h4 className="font-medium text-green-200 text-xxs leading-tight truncate">{task.title}</h4>
                        </div>
                        <div className="flex flex-col items-center ml-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full mb-0.5"></div>
                          <span className="text-xxs text-green-400 font-bold">已反馈</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无工作任务</p>
            )}
          </div>

          {/* 2. 工作通知 - 单栏滑动 */}
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30">
            <h3 className="text-xs font-medium text-blue-300 px-3 py-2 border-b border-slate-700/30">工作通知</h3>
            {dashboardData.workNotifications && dashboardData.workNotifications.length > 0 ? (
              <div className="h-32 overflow-hidden px-1 pt-1">
                <div
                  className="infinite-scroll-list"
                  style={{
                    animation: `workNotificationsScroll ${dashboardData.workNotifications.length * 4}s infinite ease-in-out`
                  }}
                >
                  <style>{`
                    @keyframes workNotificationsScroll {
                      ${dashboardData.workNotifications.map((_, index) => {
                        const totalItems = dashboardData.workNotifications?.length || 0;
                        const itemDuration = 100 / totalItems;
                        const stayPercent = itemDuration * 0.75;
                        const startPercent = index * itemDuration;
                        const stayEndPercent = startPercent + stayPercent;
                        const endPercent = (index + 1) * itemDuration;
                        const currentPosition = -index * 50;
                        const nextPosition = -(index + 1) * 50;
                        return `
                          ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                        `;
                      }).join('')}
                      100% { transform: translateY(-${(dashboardData.workNotifications?.length || 0) * 50}px); }
                    }
                  `}</style>
                  {dashboardData.workNotifications.map((notification) => (
                    <div key={`notification-${notification.id}`} className="flex items-center justify-between py-1 px-2 bg-blue-900/30 rounded mb-1 border border-blue-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-blue-500/30 text-blue-200">{notification.notification_id}</span>
                          <span className="text-xxs text-blue-400">{notification.type}</span>
                        </div>
                        <h4 className="font-medium text-blue-200 text-xxs leading-tight truncate">{notification.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(notification.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                  {dashboardData.workNotifications.map((notification) => (
                    <div key={`notification-repeat-${notification.id}`} className="flex items-center justify-between py-1 px-2 bg-blue-900/30 rounded mb-1 border border-blue-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-blue-500/30 text-blue-200">{notification.notification_id}</span>
                          <span className="text-xxs text-blue-400">{notification.type}</span>
                        </div>
                        <h4 className="font-medium text-blue-200 text-xxs leading-tight truncate">{notification.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(notification.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无工作通知</p>
            )}
          </div>

          {/* 3. 攻击源预警 - 单栏滑动 */}
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30">
            <h3 className="text-xs font-medium text-red-300 px-3 py-2 border-b border-slate-700/30">攻击源预警</h3>
            {dashboardData.attackSourceWarnings && dashboardData.attackSourceWarnings.length > 0 ? (
              <div className="h-32 overflow-hidden px-1 pt-1">
                <div
                  className="infinite-scroll-list"
                  style={{
                    animation: `attackSourceScroll ${dashboardData.attackSourceWarnings.length * 4}s infinite ease-in-out`
                  }}
                >
                  <style>{`
                    @keyframes attackSourceScroll {
                      ${dashboardData.attackSourceWarnings.map((_, index) => {
                        const totalItems = dashboardData.attackSourceWarnings?.length || 0;
                        const itemDuration = 100 / totalItems;
                        const stayPercent = itemDuration * 0.75;
                        const startPercent = index * itemDuration;
                        const stayEndPercent = startPercent + stayPercent;
                        const endPercent = (index + 1) * itemDuration;
                        const currentPosition = -index * 50;
                        const nextPosition = -(index + 1) * 50;
                        return `
                          ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                        `;
                      }).join('')}
                      100% { transform: translateY(-${(dashboardData.attackSourceWarnings?.length || 0) * 50}px); }
                    }
                  `}</style>
                  {dashboardData.attackSourceWarnings.map((warning) => (
                    <div key={`attack-warning-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-red-900/30 rounded mb-1 border border-red-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-red-500/30 text-red-200">{warning.warning_id}</span>
                          <span className="text-xxs text-red-400">点击: {warning.hits}</span>
                        </div>
                        <h4 className="font-medium text-red-200 text-xxs leading-tight truncate">{warning.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(warning.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                  {dashboardData.attackSourceWarnings.map((warning) => (
                    <div key={`attack-warning-repeat-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-red-900/30 rounded mb-1 border border-red-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-red-500/30 text-red-200">{warning.warning_id}</span>
                          <span className="text-xxs text-red-400">点击: {warning.hits}</span>
                        </div>
                        <h4 className="font-medium text-red-200 text-xxs leading-tight truncate">{warning.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(warning.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无攻击源预警</p>
            )}
          </div>

          {/* 4. 漏洞预警 - 三栏布局 */}
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30">
            <h3 className="text-xs font-medium text-orange-300 px-3 py-2 border-b border-slate-700/30">漏洞预警</h3>
            {dashboardData.vulnerabilityWarnings && dashboardData.vulnerabilityWarnings.length > 0 ? (
              <div className="flex flex-col h-48">
                {/* 上栏：状态可视化 */}
                <div className="h-8 px-3 py-1 bg-slate-800/50 border-b border-slate-700/30 flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-xxs text-orange-300">进行中: {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-xxs text-green-300">已完成: {dashboardData.vulnerabilityWarnings.filter(v => v.status === '已完成').length}</span>
                    </div>
                  </div>
                  {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length === 0 && (
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xxs">✓</span>
                      </div>
                      <span className="text-xxs text-green-300 font-medium">全部已完成</span>
                    </div>
                  )}
                </div>

                {/* 中栏：进行中滑动 */}
                {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length > 0 && (
                  <div className="flex-1 overflow-hidden border-b border-slate-700/30">
                    <div className="h-full overflow-hidden px-1 pt-1">
                      <div
                        className="infinite-scroll-list"
                        style={{
                          animation: `vulnWarningsOngoingScroll ${dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').length * 4}s infinite ease-in-out`
                        }}
                      >
                        <style>{`
                          @keyframes vulnWarningsOngoingScroll {
                            ${dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').map((_, index) => {
                              const totalItems = dashboardData.vulnerabilityWarnings?.filter(v => v.status === '进行中').length || 0;
                              const itemDuration = 100 / totalItems;
                              const stayPercent = itemDuration * 0.75;
                              const startPercent = index * itemDuration;
                              const stayEndPercent = startPercent + stayPercent;
                              const endPercent = (index + 1) * itemDuration;
                              const currentPosition = -index * 55;
                              const nextPosition = -(index + 1) * 55;
                              return `
                                ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                                ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                              `;
                            }).join('')}
                            100% { transform: translateY(-${(dashboardData.vulnerabilityWarnings?.filter(v => v.status === '进行中').length || 0) * 55}px); }
                          }
                        `}</style>
                        {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').map((warning) => (
                          <div key={`vuln-ongoing-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-orange-900/40 rounded mb-1 border border-orange-500/40" style={{height: '51px', minHeight: '51px'}}>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 mb-0.5">
                                <span className="text-xxs font-bold px-1 py-0.5 rounded bg-orange-500/30 text-orange-200">{warning.warning_id}</span>
                                <span className="text-xxs text-orange-400">{warning.risk_level}</span>
                              </div>
                              <h4 className="font-medium text-orange-200 text-xxs leading-tight truncate mb-0.5">{warning.title.split('\n')[0]}</h4>
                              <span className="text-xxs text-slate-400">反馈: {warning.feedback_deadline.split(' ')[0]}</span>
                            </div>
                            <div className="flex flex-col items-center ml-2">
                              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse mb-0.5"></div>
                              <span className="text-xxs text-orange-400 font-bold">进行中</span>
                            </div>
                          </div>
                        ))}
                        {dashboardData.vulnerabilityWarnings.filter(v => v.status === '进行中').map((warning) => (
                          <div key={`vuln-ongoing-repeat-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-orange-900/40 rounded mb-1 border border-orange-500/40" style={{height: '51px', minHeight: '51px'}}>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1 mb-0.5">
                                <span className="text-xxs font-bold px-1 py-0.5 rounded bg-orange-500/30 text-orange-200">{warning.warning_id}</span>
                                <span className="text-xxs text-orange-400">{warning.risk_level}</span>
                              </div>
                              <h4 className="font-medium text-orange-200 text-xxs leading-tight truncate mb-0.5">{warning.title.split('\n')[0]}</h4>
                              <span className="text-xxs text-slate-400">反馈: {warning.feedback_deadline.split(' ')[0]}</span>
                            </div>
                            <div className="flex flex-col items-center ml-2">
                              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse mb-0.5"></div>
                              <span className="text-xxs text-orange-400 font-bold">进行中</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* 下栏：已完成滑动 */}
                <div className="flex-1 overflow-hidden">
                  <div className="h-full overflow-y-auto px-1 pt-1">
                    {dashboardData.vulnerabilityWarnings.filter(v => v.status === '已完成').map((warning) => (
                      <div key={`vuln-finished-${warning.id}`} className="flex items-center justify-between py-1 px-2 bg-green-900/30 rounded mb-1 border border-green-500/30" style={{height: '51px', minHeight: '51px'}}>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-1 mb-0.5">
                            <span className="text-xxs font-bold px-1 py-0.5 rounded bg-green-500/30 text-green-200">{warning.warning_id}</span>
                            <span className="text-xxs text-slate-600">{warning.risk_level}</span>
                          </div>
                          <h4 className="font-medium text-green-200 text-xxs leading-tight truncate mb-0.5">{warning.title.split('\n')[0]}</h4>
                          <span className="text-xxs text-slate-500">处理: {warning.feedback_person}</span>
                        </div>
                        <div className="flex flex-col items-center ml-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full mb-0.5"></div>
                          <span className="text-xxs text-green-400 font-bold">已完成</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无漏洞预警</p>
            )}
          </div>

          {/* 5. 预警通告 - 单栏滑动 */}
          <div className="bg-slate-900/40 rounded-lg border border-slate-700/30">
            <h3 className="text-xs font-medium text-purple-300 px-3 py-2 border-b border-slate-700/30">预警通告</h3>
            {dashboardData.warningAnnouncements && dashboardData.warningAnnouncements.length > 0 ? (
              <div className="h-32 overflow-hidden px-1 pt-1">
                <div
                  className="infinite-scroll-list"
                  style={{
                    animation: `announcementsScroll ${dashboardData.warningAnnouncements.length * 4}s infinite ease-in-out`
                  }}
                >
                  <style>{`
                    @keyframes announcementsScroll {
                      ${dashboardData.warningAnnouncements.map((_, index) => {
                        const totalItems = dashboardData.warningAnnouncements?.length || 0;
                        const itemDuration = 100 / totalItems;
                        const stayPercent = itemDuration * 0.75;
                        const startPercent = index * itemDuration;
                        const stayEndPercent = startPercent + stayPercent;
                        const endPercent = (index + 1) * itemDuration;
                        const currentPosition = -index * 50;
                        const nextPosition = -(index + 1) * 50;
                        return `
                          ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                          ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                        `;
                      }).join('')}
                      100% { transform: translateY(-${(dashboardData.warningAnnouncements?.length || 0) * 50}px); }
                    }
                  `}</style>
                  {dashboardData.warningAnnouncements.map((announcement) => (
                    <div key={`announcement-${announcement.id}`} className="flex items-center justify-between py-1 px-2 bg-purple-900/30 rounded mb-1 border border-purple-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-purple-500/30 text-purple-200">{announcement.announcement_id}</span>
                          <span className="text-xxs text-purple-400">{announcement.type}</span>
                          <span className="text-xxs text-purple-400">点击: {announcement.hits}</span>
                        </div>
                        <h4 className="font-medium text-purple-200 text-xxs leading-tight truncate">{announcement.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(announcement.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                  {dashboardData.warningAnnouncements.map((announcement) => (
                    <div key={`announcement-repeat-${announcement.id}`} className="flex items-center justify-between py-1 px-2 bg-purple-900/30 rounded mb-1 border border-purple-500/30" style={{height: '46px', minHeight: '46px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1 mb-0.5">
                          <span className="text-xxs font-bold px-1 py-0.5 rounded bg-purple-500/30 text-purple-200">{announcement.announcement_id}</span>
                          <span className="text-xxs text-purple-400">{announcement.type}</span>
                          <span className="text-xxs text-purple-400">点击: {announcement.hits}</span>
                        </div>
                        <h4 className="font-medium text-purple-200 text-xxs leading-tight truncate">{announcement.title}</h4>
                      </div>
                      <span className="text-xxs text-slate-400 ml-2">{new Date(announcement.publish_time).toLocaleDateString()}</span>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-slate-500 text-xs p-3">无预警通告</p>
            )}
          </div>
        </div>
      </div>

      {/* 网省预警 - 简化版本 */}
      <div
        className="bg-black p-3 rounded-lg shadow-glow-blue flex-grow min-h-0 border border-[#00d9ff]/20"
        style={{ minHeight: '30%' }}
      >
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-sm font-medium text-[#00d9ff] uppercase tracking-widest">网省预警</h2>
          <div className="flex gap-2">
            <span className="px-2 py-1 text-xxs font-medium rounded bg-red-500/20 text-red-300">
              未闭环: {dashboardData.provinceWarnings?.filter(w => !w.is_closed).length || 0}
            </span>
            <span className="px-2 py-1 text-xxs font-medium rounded bg-green-500/20 text-green-300">
              已闭环: {dashboardData.provinceWarnings?.filter(w => w.is_closed).length || 0}
            </span>
          </div>
        </div>

        {dashboardData.provinceWarnings && dashboardData.provinceWarnings.length > 0 ? (
          <div className="h-full overflow-hidden">
            {/* 三栏布局 */}
            <div className="flex flex-col h-full">
              {/* 上栏：状态可视化 */}
              <div className="h-8 px-3 py-1 bg-slate-800/50 border-b border-slate-700/30 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-xxs text-red-300">未闭环: {dashboardData.provinceWarnings.filter(w => !w.is_closed).length}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-xxs text-green-300">已闭环: {dashboardData.provinceWarnings.filter(w => w.is_closed).length}</span>
                  </div>
                </div>
                {dashboardData.provinceWarnings.filter(w => !w.is_closed).length === 0 && (
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xxs">✓</span>
                    </div>
                    <span className="text-xxs text-green-300 font-medium">全部已闭环</span>
                  </div>
                )}
              </div>

              {/* 中栏：未闭环滑动 */}
              {dashboardData.provinceWarnings.filter(w => !w.is_closed).length > 0 && (
                <div className="flex-1 overflow-hidden border-b border-slate-700/30">
                  <div className="h-full overflow-hidden px-1 pt-1">
                    <div
                      className="infinite-scroll-list"
                      style={{
                        animation: `provinceWarningUnclosedScroll ${dashboardData.provinceWarnings.filter(w => !w.is_closed).length * 4}s infinite ease-in-out`
                      }}
                    >
                      <style>{`
                        @keyframes provinceWarningUnclosedScroll {
                          ${dashboardData.provinceWarnings.filter(w => !w.is_closed).map((_, index) => {
                            const totalItems = dashboardData.provinceWarnings?.filter(w => !w.is_closed).length || 0;
                            const itemDuration = 100 / totalItems;
                            const stayPercent = itemDuration * 0.75;
                            const startPercent = index * itemDuration;
                            const stayEndPercent = startPercent + stayPercent;
                            const endPercent = (index + 1) * itemDuration;
                            const currentPosition = -index * 60;
                            const nextPosition = -(index + 1) * 60;
                            return `
                              ${startPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                              ${stayEndPercent.toFixed(2)}% { transform: translateY(${currentPosition}px); }
                              ${endPercent.toFixed(2)}% { transform: translateY(${nextPosition}px); }
                            `;
                          }).join('')}
                          100% { transform: translateY(-${(dashboardData.provinceWarnings?.filter(w => !w.is_closed).length || 0) * 60}px); }
                        }
                      `}</style>
                      {dashboardData.provinceWarnings.filter(w => !w.is_closed).map((warning) => (
                        <div key={`unclosed-${warning.id}`} className="flex items-center justify-between py-2 px-3 bg-red-900/40 rounded-lg mb-2 border border-red-500/40" style={{height: '56px', minHeight: '56px'}}>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className={`text-xxs font-bold px-1.5 py-0.5 rounded ${warning.level === '高' ? 'bg-red-500/30 text-red-200' : warning.level === '中' ? 'bg-orange-500/30 text-orange-200' : 'bg-yellow-500/30 text-yellow-200'}`}>{warning.level}</span>
                              <span className="text-xxs text-slate-300 font-medium">{warning.province}</span>
                              <span className="text-xxs text-slate-500">#{warning.warning_id}</span>
                            </div>
                            <h4 className="font-medium text-red-200 text-xs leading-tight truncate mb-1">{warning.title}</h4>
                            <div className="flex items-center gap-2 text-xxs text-slate-400">
                              <span>反馈: {warning.feedback_person}</span>
                              <span className="text-orange-300">截止: {new Date(warning.deadline_time).toLocaleDateString()}</span>
                            </div>
                          </div>
                          <div className="flex flex-col items-center ml-2">
                            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse mb-1"></div>
                            <span className="text-xxs text-red-400 font-bold">未闭环</span>
                          </div>
                        </div>
                      ))}
                      {dashboardData.provinceWarnings.filter(w => !w.is_closed).map((warning) => (
                        <div key={`unclosed-repeat-${warning.id}`} className="flex items-center justify-between py-2 px-3 bg-red-900/40 rounded-lg mb-2 border border-red-500/40" style={{height: '56px', minHeight: '56px'}}>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className={`text-xxs font-bold px-1.5 py-0.5 rounded ${warning.level === '高' ? 'bg-red-500/30 text-red-200' : warning.level === '中' ? 'bg-orange-500/30 text-orange-200' : 'bg-yellow-500/30 text-yellow-200'}`}>{warning.level}</span>
                              <span className="text-xxs text-slate-300 font-medium">{warning.province}</span>
                              <span className="text-xxs text-slate-500">#{warning.warning_id}</span>
                            </div>
                            <h4 className="font-medium text-red-200 text-xs leading-tight truncate mb-1">{warning.title}</h4>
                            <div className="flex items-center gap-2 text-xxs text-slate-400">
                              <span>反馈: {warning.feedback_person}</span>
                              <span className="text-orange-300">截止: {new Date(warning.deadline_time).toLocaleDateString()}</span>
                            </div>
                          </div>
                          <div className="flex flex-col items-center ml-2">
                            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse mb-1"></div>
                            <span className="text-xxs text-red-400 font-bold">未闭环</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* 下栏：已闭环滑动 */}
              <div className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto px-1 pt-1">
                  {dashboardData.provinceWarnings.filter(w => w.is_closed).map((warning) => (
                    <div key={`closed-${warning.id}`} className="flex items-center justify-between py-2 px-3 bg-green-900/30 rounded-lg mb-2 border border-green-500/30" style={{height: '56px', minHeight: '56px'}}>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-xxs font-bold px-1.5 py-0.5 rounded bg-green-500/30 text-green-200">{warning.level}</span>
                          <span className="text-xxs text-slate-400 font-medium">{warning.province}</span>
                          <span className="text-xxs text-slate-600">#{warning.warning_id}</span>
                        </div>
                        <h4 className="font-medium text-green-200 text-xs leading-tight truncate mb-1">{warning.title}</h4>
                        <div className="flex items-center gap-2 text-xxs text-slate-500">
                          <span>闭环: {warning.closed_by}</span>
                          <span className="text-green-400">用时: {Math.floor((warning.response_time || 0) / 60)}h</span>
                        </div>
                      </div>
                      <div className="flex flex-col items-center ml-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full mb-1"></div>
                        <span className="text-xxs text-green-400 font-bold">已闭环</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <p className="text-slate-500 text-xs pl-1">无网省预警</p>
        )}
      </div>

      {/* 预警平台 */}
      <div
        className="bg-black p-4 rounded-lg shadow-glow-blue flex-grow min-h-0 border border-[#00d9ff]/20"
        style={{ minHeight: '35%' }}
      >
        <h3 className="text-xs font-medium text-[#00d9ff] uppercase tracking-wider mb-2">预警平台</h3>
        {/* 选项卡切换 */}
        <div className="flex mb-3 relative">
          <div className="flex bg-black/80 rounded-lg p-1 relative">
            <button
              onClick={() => setActiveTab('attack')}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors duration-200 ${activeTab === 'attack' ? 'bg-[#00d9ff]/20 text-[#00d9ff] shadow-lg' : 'text-slate-400 hover:text-slate-300'}`}
            >高危攻击事件</button>
            <button
              onClick={() => setActiveTab('host')}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors duration-200 ${activeTab === 'host' ? 'bg-[#00d9ff]/20 text-[#00d9ff] shadow-lg' : 'text-slate-400 hover:text-slate-300'}`}
            >主机安全事件</button>
          </div>
          {/* 自动切换指示器 */}
          <div className="ml-2 flex items-center">
            <div className="w-2 h-2 bg-[#00d9ff] rounded-full animate-pulse"></div>
            <span className="ml-1 text-xs text-slate-400">自动切换</span>
          </div>
        </div>
        {/* 内容区域 */}
        <div className="overflow-hidden">
          <div className="flex transition-transform duration-500 ease-in-out" style={{ transform: `translateX(${activeTab === 'attack' ? '0%' : '-100%'})` }}>
            {/* 高危攻击事件 */}
            <div className="w-full flex-shrink-0">
              <div className="overflow-hidden">
                <div className="rounded-md overflow-hidden border border-slate-700/30 max-h-48">
                  <div className="overflow-y-auto max-h-48">
                    <table className="min-w-full text-xs table-fixed">
                      <thead className="bg-slate-800/50 sticky top-0">
                        <tr>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">时间</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">源IP</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">目标IP</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">攻击类型</th>
                        </tr>
                      </thead>
                      <tbody>
                        {highRiskEvents.map((event, index) => (
                          <tr key={index} className="border-b border-slate-700/30 hover:bg-slate-800/30">
                            <td className="px-2 py-1 text-slate-400 truncate">{event.alert_time || '--'}</td>
                            <td className="px-2 py-1 text-red-400 truncate">{event.src_ip}</td>
                            <td className="px-2 py-1 text-blue-400 truncate">{event.dst_ip}</td>
                            <td className="px-2 py-1 text-orange-400 truncate">{event.alert_type}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
            {/* 主机安全事件 */}
            <div className="w-full flex-shrink-0">
              <div className="overflow-hidden">
                <div className="rounded-md overflow-hidden border border-slate-700/30 max-h-48">
                  <div className="overflow-y-auto max-h-48">
                    <table className="min-w-full text-xs table-fixed">
                      <thead className="bg-slate-800/50 sticky top-0">
                        <tr>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">时间</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">主机</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">事件类型</th>
                          <th className="px-2 py-1 text-left text-slate-300 font-medium w-1/4">风险等级</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.hostSecurityEvents?.map((event, index) => (
                          <tr key={index} className="border-b border-slate-700/30 hover:bg-slate-800/30">
                            <td className="px-2 py-1 text-slate-400 truncate">{event.timestamp}</td>
                            <td className="px-2 py-1 text-blue-400 truncate">{event.hostname}</td>
                            <td className="px-2 py-1 text-yellow-400 truncate">{event.eventType}</td>
                            <td className="px-2 py-1 text-red-400 truncate">{event.riskLevel}</td>
                          </tr>
                        )) || (
                          <tr>
                            <td colSpan={4} className="px-2 py-4 text-center text-slate-500">暂无主机安全事件</td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RightSidebar;
