# DeviceStatusPanel 组件

## 概述

`DeviceStatusPanel` 是一个专门用于显示网络安全设备状态的综合监控面板组件。它集成了三种主要的安全设备监控：WAF（Web应用防火墙）、IPS（入侵防护系统）和DDoS（分布式拒绝服务攻击防护）。

## 功能特性

### 1. WAF 状态监控
- 使用现有的 `WafStatusRealtime` 组件
- 显示WAF运行状态、在线时长、检测频率
- 实时状态指示器和脉冲动画效果
- 绿色主题配色，表示安全防护

### 2. IPS 入侵防护监控
- 全新设计的IPS状态组件
- 监控三个IPS设备（IPS-01, IPS-02, IPS-03）
- 显示威胁检测、阻断数量、规则数量、性能指标
- 紫色主题配色，突出入侵防护特性
- 智能检测率计算和可视化指示器

### 3. DDoS 防护监控
- 基于 `DDoSMonitoringCharts` 组件的设备状态部分
- 监控四个DDoS防护设备（F3/1, F3/2, G2/1, G2/2）
- 显示连接数、会话数、带宽、吞吐量
- 橙色主题配色，强调DDoS防护
- 负载百分比可视化和状态指示

## 组件结构

```
DeviceStatusPanel/
├── WafStatusRealtime (现有组件)
├── IpsStatusComponent (新建)
├── DdosStatusComponent (改造自DDoSMonitoringCharts)
└── 主面板容器
```

## 设计特点

### 视觉设计
- **科幻风格**：采用深色背景、发光边框、半透明效果
- **颜色主题**：
  - WAF: 绿色/青色系 (安全)
  - IPS: 紫色系 (防护)
  - DDoS: 橙色系 (警戒)
- **动画效果**：脉冲动画、渐变过渡、实时数据更新

### 数据可视化
- **状态指示器**：彩色圆点 + 动画效果
- **负载条**：垂直进度条显示设备负载
- **实时数据**：每30秒自动更新
- **状态图例**：清晰的颜色编码说明

### 响应式布局
- 自适应容器宽度
- 设备卡片弹性布局
- 紧凑的信息展示

## 使用方法

### 基本使用

```tsx
import DeviceStatusPanel from './components/DeviceStatusPanel';

function App() {
  return (
    <div className="your-container">
      <DeviceStatusPanel />
    </div>
  );
}
```

### 在仪表板中使用

```tsx
// 在左侧边栏或专门的监控区域
<section className="device-monitoring">
  <DeviceStatusPanel />
</section>
```

## 数据更新机制

- **自动更新**：每30秒自动刷新设备数据
- **模拟数据**：使用随机数生成真实的监控数据
- **状态变化**：设备可能随机离线（模拟真实环境）

## 状态指示说明

### WAF状态
- 🟢 正常运行：绿色脉冲指示器
- 📊 在线时长：实时计时器
- ⚡ 检测频率：3秒间隔

### IPS状态
- 🟣 防护中：紫色脉冲指示器
- 📈 检测率：基于威胁/阻断比例
- 🎯 性能等级：优秀(>90%) / 良好(70-90%) / 警告(<70%)

### DDoS状态
- 🟠 在线：橙色脉冲指示器
- 📊 负载指示：正常(<60%) / 警告(60-80%) / 危险(>80%)
- 💾 实时指标：连接、会话、带宽、吞吐量

## 技术实现

- **React Hooks**：useState, useEffect
- **TypeScript**：完整类型定义
- **Tailwind CSS**：响应式样式和动画
- **SVG图标**：矢量图标支持
- **定时器**：setInterval实现数据更新

## 扩展性

组件设计具有良好的扩展性：
- 可以轻松添加新的设备类型
- 支持自定义主题颜色
- 可以接入真实的API数据源
- 支持自定义更新频率

## 依赖关系

- `WafStatusRealtime` 组件
- React 18+
- TypeScript
- Tailwind CSS
