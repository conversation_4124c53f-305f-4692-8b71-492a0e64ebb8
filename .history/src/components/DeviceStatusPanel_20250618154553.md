# DeviceStatusPanel 组件

## 概述

`DeviceStatusPanel` 是一个专门用于显示网络安全设备状态的科幻风格综合监控面板组件。它集成了三种主要的安全设备监控：WAF（Web应用防火墙）、IPS（入侵防护系统）和DDoS（分布式拒绝服务攻击防护），采用未来科技感的视觉设计。

## 功能特性

### 1. WAF 状态监控
- **设备数量**: 2台设备 (WAF-01, WAF-02)
- **连接检测**: 通过 PING 检测设备连接状态
- **实时指标**: 显示ping响应时间、运行时长、请求数、拦截数
- **视觉设计**: 青色主题配色，科幻边框和发光效果
- **状态指示**: 实时ping时间条形图和状态灯

### 2. IPS 入侵防护监控
- **设备数量**: 2台设备 (IPS-01, IPS-02)
- **连接检测**: 通过 PING 检测设备连接状态
- **实时指标**: 显示威胁检测、阻断数量、规则数量、性能指标
- **视觉设计**: 紫色主题配色，环形检测率进度条
- **状态指示**: ping响应时间和检测率可视化

### 3. DDoS 防护监控
- **设备数量**: 4台设备 (F3/1, F3/2, G2/1, G2/2)
- **负载监控**: 基于连接数计算负载百分比
- **实时指标**: 显示连接数、会话数、带宽、吞吐量
- **视觉设计**: 橙色主题配色，负载条形图
- **状态指示**: 负载百分比和运行状态

## 组件结构

```
DeviceStatusPanel/
├── WafStatusComponent (全新设计)
├── IpsStatusComponent (全新设计)
├── DdosStatusComponent (重新设计)
└── 科幻主面板容器
```

## 设计特点

### 科幻视觉设计
- **未来科技感**：深色渐变背景、霓虹发光边框、半透明玻璃效果
- **颜色主题**：
  - WAF: 青色系 (#06B6D4) - 代表防护屏障
  - IPS: 紫色系 (#9333EA) - 代表入侵检测
  - DDoS: 橙色系 (#F97316) - 代表攻击防御
- **动画效果**：
  - 扫描线动画 (scan-line)
  - 脉冲发光效果 (cyber-pulse)
  - 全息投影效果 (hologram)
  - 数据流动画 (data-flow)

### 高级数据可视化
- **PING状态**：实时响应时间条形图，颜色编码延迟等级
- **环形进度条**：IPS检测率的SVG环形显示
- **负载指示器**：DDoS设备负载的渐变条形图
- **状态灯**：发光圆点指示器，带阴影效果
- **实时更新**：WAF/IPS每5秒，DDoS每10秒更新

### 响应式科幻布局
- **网格系统**：2x2设备卡片布局
- **悬停效果**：卡片缩放和发光增强
- **装饰元素**：网格背景、扫描线、光点装饰
- **自适应间距**：根据内容自动调整卡片大小

## 使用方法

### 基本使用

```tsx
import DeviceStatusPanel from './components/DeviceStatusPanel';

function App() {
  return (
    <div className="your-container">
      <DeviceStatusPanel />
    </div>
  );
}
```

### 在仪表板中使用

```tsx
// 在左侧边栏或专门的监控区域
<section className="device-monitoring">
  <DeviceStatusPanel />
</section>
```

## 数据更新机制

- **自动更新**：每30秒自动刷新设备数据
- **模拟数据**：使用随机数生成真实的监控数据
- **状态变化**：设备可能随机离线（模拟真实环境）

## 状态指示说明

### WAF状态
- 🟢 正常运行：绿色脉冲指示器
- 📊 在线时长：实时计时器
- ⚡ 检测频率：3秒间隔

### IPS状态
- 🟣 防护中：紫色脉冲指示器
- 📈 检测率：基于威胁/阻断比例
- 🎯 性能等级：优秀(>90%) / 良好(70-90%) / 警告(<70%)

### DDoS状态
- 🟠 在线：橙色脉冲指示器
- 📊 负载指示：正常(<60%) / 警告(60-80%) / 危险(>80%)
- 💾 实时指标：连接、会话、带宽、吞吐量

## 技术实现

- **React Hooks**：useState, useEffect
- **TypeScript**：完整类型定义
- **Tailwind CSS**：响应式样式和动画
- **SVG图标**：矢量图标支持
- **定时器**：setInterval实现数据更新

## 扩展性

组件设计具有良好的扩展性：
- 可以轻松添加新的设备类型
- 支持自定义主题颜色
- 可以接入真实的API数据源
- 支持自定义更新频率

## 依赖关系

- `WafStatusRealtime` 组件
- React 18+
- TypeScript
- Tailwind CSS
